{"rustc": 13226066032359371072, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 17257705230225558938, "path": 10812277176400524412, "deps": [[1988483478007900009, "unicode_ident", false, 6255246489942040306], [3060637413840920116, "proc_macro2", false, 11729490756874729641], [17990358020177143287, "quote", false, 9643067268783258341]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-9ddec9eadd1af904/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}